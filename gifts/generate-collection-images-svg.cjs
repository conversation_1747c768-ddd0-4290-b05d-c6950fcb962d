const fs = require("fs");
const path = require("path");

const INPUT_FILE = "./star-gifts-cleaned.json";
const IMAGES_DIR = "./images";

// Ensure images directory exists
if (!fs.existsSync(IMAGES_DIR)) {
  fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

function generateCollectionSVG(collectionId, emoji, stars, isLimited, isSoldOut, isBirthday) {
  // Determine background colors based on status
  let bgGradient;
  if (isBirthday) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#FFE5B4;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#FFCCCB;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else if (isLimited) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#E6E6FA;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#DDA0DD;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else if (isSoldOut) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#D3D3D3;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#A9A9A9;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#E0F6FF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#87CEEB;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  }

  // Status badges
  let statusBadges = "";
  let badgeY = 120;
  
  if (isBirthday) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#FF69B4" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">🎂</text>`;
    badgeY += 60;
  }
  
  if (isLimited) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#9370DB" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">⚡</text>`;
    badgeY += 60;
  }
  
  if (isSoldOut) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#FF6347" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">❌</text>`;
  }

  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  ${bgGradient}
  
  <!-- Background -->
  <rect width="512" height="512" fill="url(#bg)"/>
  
  <!-- Border -->
  <rect x="2" y="2" width="508" height="508" fill="none" stroke="#333" stroke-width="4"/>
  
  <!-- Main emoji -->
  <text x="256" y="220" text-anchor="middle" font-size="120" font-family="Arial, sans-serif">${emoji}</text>
  
  <!-- Stars badge -->
  <circle cx="400" cy="60" r="40" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
  <text x="400" y="50" text-anchor="middle" font-size="24" fill="#FFF">⭐</text>
  <text x="400" y="72" text-anchor="middle" font-size="16" fill="#000" font-weight="bold">${stars}</text>
  
  <!-- Status badges -->
  ${statusBadges}
  
  <!-- Collection ID background -->
  <rect x="0" y="420" width="512" height="92" fill="rgba(0, 0, 0, 0.8)"/>
  
  <!-- Collection ID text -->
  <text x="256" y="450" text-anchor="middle" font-size="24" fill="#FFF" font-family="Arial, sans-serif">Collection ID</text>
  <text x="256" y="480" text-anchor="middle" font-size="20" fill="#FFF" font-family="monospace">${collectionId}</text>
</svg>`;

  return svg;
}

function generateCollectionImages() {
  try {
    console.log("📖 Reading gifts data...");
    const rawData = fs.readFileSync(INPUT_FILE, "utf8");
    const data = JSON.parse(rawData);

    console.log(`🎁 Processing ${data.gifts.length} gifts...`);

    let processedCount = 0;
    let skippedCount = 0;

    data.gifts.forEach((gift, index) => {
      try {
        const collectionId = gift.id.toString();
        const stars = gift.stars.toString();
        const isLimited = gift.limited || false;
        const isSoldOut = gift.soldOut || false;
        const isBirthday = gift.birthday || false;

        // Extract emoji from sticker attributes
        let emoji = "🎁"; // Default emoji
        if (gift.sticker && gift.sticker.attributes) {
          const customEmojiAttr = gift.sticker.attributes.find(
            attr => attr.className === "DocumentAttributeCustomEmoji"
          );
          if (customEmojiAttr && customEmojiAttr.alt) {
            emoji = customEmojiAttr.alt;
          }
        }

        // Generate SVG
        const svg = generateCollectionSVG(
          collectionId,
          emoji,
          stars,
          isLimited,
          isSoldOut,
          isBirthday
        );

        // Save SVG file
        const filename = `${collectionId}.svg`;
        const filepath = path.join(IMAGES_DIR, filename);
        
        fs.writeFileSync(filepath, svg, "utf8");

        processedCount++;

        if ((index + 1) % 10 === 0) {
          console.log(`✅ Processed ${index + 1}/${data.gifts.length} gifts...`);
        }

      } catch (error) {
        console.error(`❌ Error processing gift ${index + 1}:`, error.message);
        skippedCount++;
      }
    });

    console.log("\n🎉 SVG generation completed!");
    console.log(`📊 Results:`);
    console.log(`   ✅ Successfully processed: ${processedCount} images`);
    console.log(`   ❌ Skipped due to errors: ${skippedCount} images`);
    console.log(`📁 SVG files saved to: ${IMAGES_DIR}`);

    // Generate summary file
    const summaryData = {
      timestamp: new Date().toISOString(),
      totalGifts: data.gifts.length,
      processedCount,
      skippedCount,
      imagesDirectory: IMAGES_DIR
    };

    fs.writeFileSync(
      path.join(IMAGES_DIR, "generation-summary.json"),
      JSON.stringify(summaryData, null, 2)
    );

    console.log(`📋 Generation summary saved to: ${path.join(IMAGES_DIR, "generation-summary.json")}`);

  } catch (error) {
    console.error("❌ Error reading gifts data:", error.message);
    process.exit(1);
  }
}

console.log("🎨 Starting collection SVG generation...");
generateCollectionImages();
