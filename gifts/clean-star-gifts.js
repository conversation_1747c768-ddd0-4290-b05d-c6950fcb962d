const fs = require("fs");
const path = require("path");

const INPUT_FILE = "./star-gifts.json";
const OUTPUT_FILE = "./star-gifts-cleaned.json";

function cleanStarGifts() {
  try {
    // Read the JSON file
    const rawData = fs.readFileSync(INPUT_FILE, "utf8");
    const data = JSON.parse(rawData);

    console.log(`Processing ${data.gifts.length} gifts...`);

    // Clean each gift
    data.gifts.forEach((gift, index) => {
      if (gift.sticker) {
        // Remove fileReference
        if (gift.sticker.fileReference) {
          delete gift.sticker.fileReference;
        }

        // Remove thumbs bytes data
        if (gift.sticker.thumbs && Array.isArray(gift.sticker.thumbs)) {
          gift.sticker.thumbs.forEach((thumb) => {
            if (thumb.bytes && thumb.bytes.data) {
              delete thumb.bytes.data;
            }
          });
        }
      }

      if ((index + 1) % 10 === 0) {
        console.log(`Processed ${index + 1} gifts...`);
      }
    });

    // Write cleaned data to output file
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(data, null, 2));

    // Get file sizes for comparison
    const originalSize = fs.statSync(INPUT_FILE).size;
    const cleanedSize = fs.statSync(OUTPUT_FILE).size;
    const savedBytes = originalSize - cleanedSize;
    const savedPercentage = ((savedBytes / originalSize) * 100).toFixed(2);

    console.log("\n✅ Cleaning completed!");
    console.log(`📊 File size reduction:`);
    console.log(`   Original: ${(originalSize / 1024).toFixed(2)} KB`);
    console.log(`   Cleaned:  ${(cleanedSize / 1024).toFixed(2)} KB`);
    console.log(
      `   Saved:    ${(savedBytes / 1024).toFixed(2)} KB (${savedPercentage}%)`
    );
    console.log(`📁 Output saved to: ${OUTPUT_FILE}`);
  } catch (error) {
    console.error("❌ Error processing file:", error.message);
    process.exit(1);
  }
}

// Run the script
cleanStarGifts();
